package com.cdz360.iot.ess.model.ts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 电气设备监控时序数据模型 对应表：energy.elec_equip_monitor_super
 */
@Data
@Accessors(chain = true)
@Schema(description = "电气设备监控时序数据")
public class ElecEquipMonitorPo {

    // 时间字段
    @Schema(description = "设备采集数据时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime ts;

    // TAG字段
    @Schema(description = "设备编码", required = true)
    private String equipCode;

    // 数据字段
    @Schema(description = "数据采集时间字符串")
    private String dataTime;

    // 电能质量参数
    @Schema(description = "电能 (kWh)")
    private Double en;

    // 电压参数 (V)
    @Schema(description = "A相电压")
    private Double ua;
    @Schema(description = "B相电压")
    private Double ub;
    @Schema(description = "C相电压")
    private Double uc;
    @Schema(description = "AB线电压")
    private Double uab;
    @Schema(description = "BC线电压")
    private Double ubc;
    @Schema(description = "CA线电压")
    private Double uca;

    // 电流参数 (A)
    @Schema(description = "A相电流")
    private Double ia;
    @Schema(description = "B相电流")
    private Double ib;
    @Schema(description = "C相电流")
    private Double ic;

    // 功率参数
    @Schema(description = "有功功率 (kW)")
    private Double p;
    @Schema(description = "无功功率 (kVar)")
    private Double q;
    @Schema(description = "视在功率 (kVA)")
    private Double s;
    @Schema(description = "功率因数")
    private Double pf;
    @Schema(description = "频率 (Hz)")
    private Double f;

    // 电能质量指标
    @Schema(description = "有功电能 (kWh)")
    private Double rae;
    @Schema(description = "正向电能 (kWh)")
    private Double fre;
    @Schema(description = "反向电能 (kWh)")
    private Double rre;

    // 需量参数
    @Schema(description = "当前需量 (kW)")
    private Double cd;
    @Schema(description = "最大需量 (kW)")
    private Double md;
}
