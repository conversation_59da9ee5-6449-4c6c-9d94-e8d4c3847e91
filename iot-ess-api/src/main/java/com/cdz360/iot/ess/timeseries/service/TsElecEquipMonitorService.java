package com.cdz360.iot.ess.timeseries.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.model.ts.ElecEquipMonitorPo;
import com.cdz360.iot.ess.utils.ts.TsElecEquipMonitorUtil;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 电气设备监控服务类
 */
@Slf4j
@Service
public class TsElecEquipMonitorService {

    private static final String DATE_TIME_FORMATTER_STR = "yyyy-MM-dd HH:mm:ss.SSS";

    @Autowired
    private TsElecEquipMonitorUtil elecEquipMonitorDao;

    /**
     * 创建子表
     */
    public void createTable(String equipCode) {
        elecEquipMonitorDao.createTable(equipCode);
    }

    /**
     * 保存单条数据
     */
    public BaseResponse saveData(ElecEquipMonitorPo data) {
        // 参数验证
        checkAndSetData(data);

        // 保存数据
        elecEquipMonitorDao.insertData(data);

        log.info("保存电气设备监控数据成功: {}", data.getEquipCode());
        return RestUtils.success();
    }

    /**
     * 批量保存数据 - 高性能
     */
    @Transactional
    public BaseResponse batchSaveData(List<ElecEquipMonitorPo> dataList) {
        if (dataList.isEmpty()) {
            log.warn("批量保存数据为空");
            return RestUtils.success();
        }

        // 参数验证
        dataList.forEach(this::checkAndSetData);

        // 批量保存数据
        elecEquipMonitorDao.batchInsertData(dataList);

        log.info("批量保存电气设备监控数据成功，数量: {}", dataList.size());
        return RestUtils.success();
    }

    /**
     * 查询时间范围内的数据
     */
    public ListResponse<ElecEquipMonitorPo> queryByTimeRange(String equipCode, LocalDateTime start,
        LocalDateTime end) {
        return RestUtils.buildListResponse(
            elecEquipMonitorDao.queryByTimeRange(equipCode, start, end));
    }

    /**
     * 查询最新数据
     */
    public ObjectResponse<ElecEquipMonitorPo> queryLatest(String equipCode) {
        return RestUtils.buildObjectResponse(elecEquipMonitorDao.queryLatest(equipCode));
    }

    /**
     * 查询小时统计数据
     */
    public ListResponse<TsElecEquipMonitorUtil.ElecEquipHourlyStats> queryHourlyStats(
        String equipCode, LocalDateTime start, LocalDateTime end) {
        return RestUtils.buildListResponse(
            elecEquipMonitorDao.queryHourlyStats(equipCode, start, end));
    }

    /**
     * 查询设备当日电能数据
     */
    public ObjectResponse<ElecEquipDailyStats> queryDailyStats(String equipCode,
        LocalDateTime date) {
        LocalDateTime startOfDay = date.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);

        ListResponse<ElecEquipMonitorPo> dayData = queryByTimeRange(equipCode, startOfDay,
            endOfDay);

        if (dayData.getData().isEmpty()) {
            return RestUtils.buildObjectResponse(
                new ElecEquipDailyStats(equipCode, date, 0.0, 0.0, 0.0, 0.0));
        }
        List<ElecEquipMonitorPo> dayDataList = dayData.getData();

        // 计算当日统计数据
        double totalEnergy = dayDataList.stream()
            .mapToDouble(d -> d.getEn() != null ? d.getEn() : 0.0)
            .sum();

        double avgPower = dayDataList.stream()
            .mapToDouble(d -> d.getP() != null ? d.getP() : 0.0)
            .average()
            .orElse(0.0);

        double maxPower = dayDataList.stream()
            .mapToDouble(d -> d.getP() != null ? d.getP() : 0.0)
            .max()
            .orElse(0.0);

        double minPower = dayDataList.stream()
            .mapToDouble(d -> d.getP() != null ? d.getP() : 0.0)
            .min()
            .orElse(0.0);

        return RestUtils.buildObjectResponse(
            new ElecEquipDailyStats(equipCode, date, totalEnergy, avgPower, maxPower, minPower));
    }

    /**
     * 数据验证
     */
    private void checkAndSetData(ElecEquipMonitorPo data) {
        if (data == null) {
            throw new DcArgumentException("数据不能为空");
        }
        if (data.getEquipCode() == null || data.getEquipCode().trim().isEmpty()) {
            throw new DcArgumentException("设备编码不能为空");
        }
        if (data.getTs() == null) {
            throw new DcArgumentException("采集时间不能为空");
        }

        // 自动设置dataTime字段
        if (data.getDataTime() == null) {
            data.setDataTime(DateUtils.toStringFormat(new Date(), DATE_TIME_FORMATTER_STR));
        }
    }

    /**
     * 设备日统计数据模型
     */
    @Data
    public static class ElecEquipDailyStats {

        private String equipCode;
        private LocalDateTime date;
        private Double totalEnergy;
        private Double avgPower;
        private Double maxPower;
        private Double minPower;

        public ElecEquipDailyStats(String equipCode, LocalDateTime date,
            Double totalEnergy, Double avgPower, Double maxPower, Double minPower) {
            this.equipCode = equipCode;
            this.date = date;
            this.totalEnergy = totalEnergy;
            this.avgPower = avgPower;
            this.maxPower = maxPower;
            this.minPower = minPower;
        }
    }
}
