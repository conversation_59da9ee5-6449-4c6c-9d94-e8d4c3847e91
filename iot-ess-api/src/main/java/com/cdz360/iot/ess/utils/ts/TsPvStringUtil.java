package com.cdz360.iot.ess.utils.ts;

import com.cdz360.iot.ess.cfg.ts.TDengineSqlConstants;
import com.cdz360.iot.ess.model.ts.PvStringPo;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * 光伏组串数据访问类
 */
@Slf4j
@Repository
public class TsPvStringUtil extends TDengineBaseUtil {

    public TsPvStringUtil(org.springframework.jdbc.core.JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * 创建子表
     */
    public void createTable(String inverterNo) {
        String sql = String.format(TDengineSqlConstants.CREATE_PV_STRING_TABLE, inverterNo);
        updateWithRetry(sql, inverterNo);
        log.info("创建光伏组串子表成功: pv_string_{}", inverterNo);
    }

    /**
     * 单条插入数据
     */
    public void insertData(PvStringPo data) {
        try {
            String sql = TDengineSqlConstants.INSERT_PV_STRING_DATA;
            updateWithRetry(sql,
                data.getDno(),              // 子表名
                data.getDno(),              // TAG值
                Timestamp.valueOf(data.getTs()),
                Timestamp.valueOf(data.getSts()),
                data.getIdx(),
                data.getU(),
                data.getI()
            );
            log.debug("插入光伏组串数据成功: {}", data.getDno());
        } catch (Exception e) {
            log.error("插入光伏组串数据失败: {}, 错误: {}", data.getDno(), e.getMessage());
            throw e;
        }
    }

    /**
     * 批量插入数据
     */
    public void batchInsertData(List<PvStringPo> dataList) {
        if (dataList.isEmpty()) {
            return;
        }

        String sql = TDengineSqlConstants.INSERT_PV_STRING_DATA;
        batchInsert(sql, dataList, (ps, data) -> {
            ps.setString(1, data.getDno());
            ps.setString(2, data.getDno());
            ps.setTimestamp(3, Timestamp.valueOf(data.getTs()));
            ps.setTimestamp(4, Timestamp.valueOf(data.getSts()));
            ps.setObject(5, data.getIdx());
            ps.setObject(6, data.getU());
            ps.setObject(7, data.getI());
        });
    }

    /**
     * 查询时间范围内的数据
     */
    public List<PvStringPo> queryByTimeRange(String inverterNo, LocalDateTime start,
        LocalDateTime end) {
        String sql = TDengineSqlConstants.QUERY_PV_STRING_BY_TIME_RANGE;
        return jdbcTemplate.query(sql,
            new Object[]{inverterNo, Timestamp.valueOf(start), Timestamp.valueOf(end)},
            new BeanPropertyRowMapper<>(PvStringPo.class));
    }

    /**
     * 查询最新数据
     */
    public PvStringPo queryLatest(String inverterNo) {
        String sql = TDengineSqlConstants.QUERY_PV_STRING_LATEST;
        List<PvStringPo> results = jdbcTemplate.query(sql,
            new Object[]{inverterNo},
            new BeanPropertyRowMapper<>(PvStringPo.class));
        return results.isEmpty() ? null : results.get(0);
    }
}
