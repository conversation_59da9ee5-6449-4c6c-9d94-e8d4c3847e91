package com.cdz360.iot.ess.model.ts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 光伏逆变器时序数据模型 对应表：energy.pv_gti_super
 */
@Data
@Accessors(chain = true)
@Schema(description = "光伏逆变器时序数据")
public class PvGtiPo {

    // 时间字段
    @Schema(description = "设备采集数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime ts;

    @Schema(description = "服务器接收到数据的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime sts;

    // TAG字段
    @Schema(description = "设备编号", required = true)
    private String dno;

    // 交流侧电压
    @Schema(description = "AB线电压")
    private Double uab;
    @Schema(description = "BC线电压")
    private Double ubc;
    @Schema(description = "CA线电压")
    private Double uca;
    @Schema(description = "A相电压")
    private Double ua;
    @Schema(description = "B相电压")
    private Double ub;
    @Schema(description = "C相电压")
    private Double uc;

    // 交流侧电流
    @Schema(description = "A相电流")
    private Double ia;
    @Schema(description = "B相电流")
    private Double ib;
    @Schema(description = "C相电流")
    private Double ic;

    // 频率
    @Schema(description = "A相频率")
    private Double fa;
    @Schema(description = "B相频率")
    private Double fb;
    @Schema(description = "C相频率")
    private Double fc;

    // 功率与电能质量
    @Schema(description = "有功功率 (kW)")
    private Double p;
    @Schema(description = "无功功率 (kVar)")
    private Double q;
    @Schema(description = "视在功率 (kVA)")
    private Double s;
    @Schema(description = "功率因数")
    private Double pf;
    @Schema(description = "电网频率 (Hz)")
    private Double f;

    // 直流侧
    @Schema(description = "直流侧有功功率 (kW)")
    private Double ip;
    @Schema(description = "直流侧绝缘阻抗 (MΩ)")
    private Double iso;

    // 整体性能与状态
    @Schema(description = "转换效率 (%)")
    private Double te;
    @Schema(description = "内部温度 (°C)")
    private Double it;
    @Schema(description = "运行状态")
    private Integer rs;

    // 发电量统计
    @Schema(description = "累计发电量 (kWh)")
    private Double epe;
    @Schema(description = "累计发电时间 (h)")
    private Double ah;
    @Schema(description = "当日发电量 (kWh)")
    private Double tk;
}
