package com.cdz360.iot.ess.timeseries.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.model.ts.PvGtiPo;
import com.cdz360.iot.ess.utils.ts.TsPvGtiUtil;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 光伏逆变器服务类
 */
@Slf4j
@Service
public class TsPvGtiService {

    @Autowired
    private TsPvGtiUtil pvGtiDao;

    /**
     * 创建子表
     */
    public void createTable(String deviceNo) {
        pvGtiDao.createTable(deviceNo);
    }

    /**
     * 保存单条数据
     */
    public void saveData(PvGtiPo data) {
        // 参数验证
        checkAndSetData(data);

        // 保存数据
        pvGtiDao.insertData(data);

        log.info("保存光伏逆变器数据成功: {}", data.getDno());
    }

    /**
     * 批量保存数据 - 高性能
     */
    @Transactional
    public void batchSaveData(List<PvGtiPo> dataList) {
        if (dataList.isEmpty()) {
            log.warn("批量保存数据为空");
            return;
        }

        // 参数验证
        dataList.forEach(this::checkAndSetData);

        // 批量保存数据
        pvGtiDao.batchInsertData(dataList);

        log.info("批量保存光伏逆变器数据成功，数量: {}", dataList.size());
    }

    /**
     * 查询时间范围内的数据
     */
    public ListResponse<PvGtiPo> queryByTimeRange(String deviceNo, LocalDateTime start,
        LocalDateTime end) {
        return RestUtils.buildListResponse(pvGtiDao.queryByTimeRange(deviceNo, start, end));
    }

    /**
     * 查询最新数据
     */
    public ObjectResponse<PvGtiPo> queryLatest(String deviceNo) {
        return RestUtils.buildObjectResponse(pvGtiDao.queryLatest(deviceNo));
    }

    /**
     * 查询小时统计数据
     */
    public ListResponse<TsPvGtiUtil.PvGtiHourlyStats> queryHourlyStats(String deviceNo,
        LocalDateTime start, LocalDateTime end) {
        return RestUtils.buildListResponse(pvGtiDao.queryHourlyStats(deviceNo, start, end));
    }

    /**
     * 数据验证
     */
    private void checkAndSetData(PvGtiPo data) {
        if (data == null) {
            throw new DcArgumentException("数据不能为空");
        }
        if (data.getDno() == null || data.getDno().trim().isEmpty()) {
            throw new DcArgumentException("设备编号不能为空");
        }
        if (data.getTs() == null) {
            throw new DcArgumentException("采集时间不能为空");
        }
        if (data.getSts() == null) {
            data.setSts(LocalDateTime.now()); // 自动设置服务器时间
        }
    }
}
