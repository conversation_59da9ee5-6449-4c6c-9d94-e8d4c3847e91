package com.cdz360.iot.ess.timeseries.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.iot.ess.model.ts.PvStringPo;
import com.cdz360.iot.ess.utils.ts.TsPvStringUtil;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 光伏组串服务类
 */
@Slf4j
@Service
public class TsPvStringService {

    @Autowired
    private TsPvStringUtil pvStringDao;

    /**
     * 创建子表
     */
    public void createTable(String deviceNo) {
        pvStringDao.createTable(deviceNo);
    }

    /**
     * 保存单条数据
     */
    public BaseResponse saveData(PvStringPo data) {
        checkAndSetData(data);
        pvStringDao.insertData(data);
        log.info("保存光伏组串数据成功: {}", data.getDno());
        return RestUtils.success();
    }

    /**
     * 批量保存数据
     */
    @Transactional
    public BaseResponse batchSaveData(List<PvStringPo> dataList) {
        if (dataList.isEmpty()) {
            return RestUtils.success();
        }

        dataList.forEach(this::checkAndSetData);

        pvStringDao.batchInsertData(dataList);
        log.info("批量保存光伏组串数据成功，数量: {}", dataList.size());
        return RestUtils.success();
    }

    /**
     * 查询时间范围内的数据
     */
    public ListResponse<PvStringPo> queryByTimeRange(String inverterNo, LocalDateTime start,
        LocalDateTime end) {
        return RestUtils.buildListResponse(pvStringDao.queryByTimeRange(inverterNo, start, end));
    }

    /**
     * 查询最新数据
     */
    public ObjectResponse<PvStringPo> queryLatest(String inverterNo) {
        return RestUtils.buildObjectResponse(pvStringDao.queryLatest(inverterNo));
    }

    /**
     * 数据验证
     */
    private void checkAndSetData(PvStringPo data) {
        if (data == null) {
            throw new DcArgumentException("数据不能为空");
        }
        if (data.getDno() == null || data.getDno().trim().isEmpty()) {
            throw new DcArgumentException("逆变器编号不能为空");
        }
        if (data.getTs() == null) {
            throw new DcArgumentException("采集时间不能为空");
        }
        if (data.getSts() == null) {
            data.setSts(LocalDateTime.now());
        }
    }
}
