package com.cdz360.iot.ess.cfg.ts;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.time.Instant;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * TDengine数据源配置
 */
@Configuration
public class TDengineConfig {

    @Value("${tdengine.datasource.driver-class-name}")
    private String driverClassName;

    @Value("${tdengine.datasource.url}")
    private String url;

    @Value("${tdengine.datasource.username}")
    private String username;

    @Value("${tdengine.datasource.password}")
    private String password;

    @Value("${tdengine.datasource.hikari.maximumPoolSize:200}")
    private int maximumPoolSize;

    @Value("${tdengine.datasource.hikari.minimumIdle:50}")
    private int minimumIdle;

    @Value("${tdengine.datasource.hikari.connectionTimeout:30000}")
    private int connectionTimeout;

    @Value("${tdengine.datasource.hikari.idleTimeout:600000}")
    private int idleTimeout;

    @Value("${tdengine.datasource.hikari.maxLifetime:1800000}")
    private int maxLifetime;

    @Value("${tdengine.datasource.hikari.poolName:TDengineHikariPool}")
    private String poolName;

    @Bean(name = "tDengineDataSource")
    public DataSource tDengineDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);

        // 针对TDengine时序数据优化的连接池配置
        config.setMaximumPoolSize(maximumPoolSize);
        config.setMinimumIdle(minimumIdle);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setConnectionTimeout(connectionTimeout);
        config.setPoolName(poolName);

        return new HikariDataSource(config);
    }

    @Bean(name = "tDengineJdbcTemplate")
    public JdbcTemplate tDengineJdbcTemplate(
        @Qualifier("tDengineDataSource") DataSource dataSource) {
        JdbcTemplate template = new JdbcTemplate(dataSource);
//        template.setFetchSize(1000); // 优化查询性能
        return template;
    }

    /**
     * TDengine健康检查
     */
    @Component
    public static class TDengineHealthIndicator implements HealthIndicator {

        private final JdbcTemplate tDengineJdbcTemplate;

        public TDengineHealthIndicator(
            @Qualifier("tDengineJdbcTemplate") JdbcTemplate tDengineJdbcTemplate) {
            this.tDengineJdbcTemplate = tDengineJdbcTemplate;
        }

        @Override
        public Health health() {
            try {
                Integer result = tDengineJdbcTemplate.queryForObject("SELECT 1", Integer.class);
                return Health.up()
                    .withDetail("database", "TDengine")
                    .withDetail("status", "连接正常")
                    .withDetail("timestamp", Instant.now())
                    .build();
            } catch (Exception e) {
                return Health.down()
                    .withDetail("database", "TDengine")
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", Instant.now())
                    .build();
            }
        }
    }
}
