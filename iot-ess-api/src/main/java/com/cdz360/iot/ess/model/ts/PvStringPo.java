package com.cdz360.iot.ess.model.ts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 光伏组串时序数据模型 对应表：energy.pv_string_super
 */
@Data
@Accessors(chain = true)
@Schema(description = "光伏组串时序数据")
public class PvStringPo {

    // 时间字段
    @Schema(description = "设备采集数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime ts;

    @Schema(description = "服务器接收到数据的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime sts;

    // TAG字段
    @Schema(description = "所属逆变器编号", required = true)
    private String dno;

    // 数据字段
    @Schema(description = "组串ID")
    private Integer idx;
    @Schema(description = "直流电压 (V)")
    private Double u;
    @Schema(description = "直流电流 (A)")
    private Double i;
}
