package com.cdz360.iot.ess.utils.ts;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;

/**
 * TDengine基础数据访问类 - 提供重试机制和异常处理
 */
@Slf4j
@Repository
public class TDengineBaseUtil {

    protected final JdbcTemplate jdbcTemplate;

    public TDengineBaseUtil(@Qualifier("tDengineJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 执行查询 - 带重试机制
     */
    @Retryable(value = {SQLException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public <T> List<T> queryWithRetry(String sql, Object[] params, Class<T> clazz) {
        try {
            return jdbcTemplate.query(sql, params, (rs, rowNum) -> {
                // 使用反射或手动映射，这里简化处理
                // 实际项目中建议使用BeanPropertyRowMapper或自定义RowMapper
                // TODO: 2025/8/23 WZFIX 需要具体实现
                return null;
            });
        } catch (Exception e) {
            log.error("TDengine查询失败，SQL: {}, 参数: {}, 错误: {}", sql, Arrays.toString(params),
                e.getMessage());
            throw new RuntimeException("查询失败", e);
        }
    }

    /**
     * 执行更新 - 带重试机制
     */
    @Retryable(value = {SQLException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public int updateWithRetry(String sql, Object... params) {
        try {
            return jdbcTemplate.update(sql, params);
        } catch (Exception e) {
            log.error("TDengine更新失败，SQL: {}, 参数: {}, 错误: {}", sql, Arrays.toString(params),
                e.getMessage());
            throw new RuntimeException("更新失败", e);
        }
    }

    /**
     * 批量插入 - 高性能
     */
    public <T> void batchInsert(String sql, List<T> dataList, BatchParameterSetter<T> setter) {
        try {
            jdbcTemplate.batchUpdate(sql, dataList, dataList.size(), (ps, item) -> {
                setter.setParameters(ps, item);
            });
            log.info("批量插入成功，数据量: {}", dataList.size());
        } catch (Exception e) {
            log.error("批量插入失败，SQL: {}, 数据量: {}, 错误: {}", sql, dataList.size(),
                e.getMessage());
            throw new RuntimeException("批量插入失败", e);
        }
    }

    /**
     * 重试失败后的恢复方法
     */
    @Recover
    public <T> List<T> recover(SQLException ex, String sql, Object[] params, Class<T> clazz) {
        log.error("TDengine查询重试失败，返回空结果，SQL: {}, 错误: {}", sql, ex.getMessage());
        return Collections.emptyList();
    }

    @Recover
    public int recover(SQLException ex, String sql, Object... params) {
        log.error("TDengine更新重试失败，返回0，SQL: {}, 错误: {}", sql, ex.getMessage());
        return 0;
    }

    /**
     * 批量参数设置接口
     */
    @FunctionalInterface
    public interface BatchParameterSetter<T> {

        void setParameters(java.sql.PreparedStatement ps, T item) throws SQLException;
    }
}
