package com.cdz360.iot.ess.utils.ts;

import com.cdz360.iot.ess.cfg.ts.TDengineSqlConstants;
import com.cdz360.iot.ess.model.ts.ElecEquipMonitorPo;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

/**
 * 电气设备监控数据访问类
 */
@Slf4j
@Repository
public class TsElecEquipMonitorUtil extends TDengineBaseUtil {

    public TsElecEquipMonitorUtil(org.springframework.jdbc.core.JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * 创建子表
     */
    public void createTable(String equipCode) {
        String sql = String.format(TDengineSqlConstants.CREATE_ELEC_EQUIP_TABLE, equipCode);
        updateWithRetry(sql, equipCode);
        log.info("创建电气设备监控子表成功: elec_equip_monitor_{}", equipCode);
    }

    /**
     * 单条插入数据
     */
    public void insertData(ElecEquipMonitorPo data) {
        try {
            String sql = TDengineSqlConstants.INSERT_ELEC_EQUIP_DATA;
            updateWithRetry(sql,
                data.getEquipCode(),        // 子表名
                data.getEquipCode(),        // TAG值
                Timestamp.valueOf(data.getTs()),
                data.getDataTime(),
                data.getEn(),
                data.getUa(), data.getUb(), data.getUc(),
                data.getUab(), data.getUbc(), data.getUca(),
                data.getIa(), data.getIb(), data.getIc(),
                data.getP(), data.getQ(), data.getS(), data.getPf(), data.getF(),
                data.getRae(), data.getFre(), data.getRre(),
                data.getCd(), data.getMd()
            );
            log.debug("插入电气设备监控数据成功: {}", data.getEquipCode());
        } catch (Exception e) {
            log.error("插入电气设备监控数据失败: {}, 错误: {}", data.getEquipCode(),
                e.getMessage());
            throw e;
        }
    }

    /**
     * 批量插入数据 - 高性能
     */
    public void batchInsertData(List<ElecEquipMonitorPo> dataList) {
        if (dataList.isEmpty()) {
            return;
        }

        String sql = TDengineSqlConstants.INSERT_ELEC_EQUIP_DATA;
        batchInsert(sql, dataList, (ps, data) -> {
            ps.setString(1, data.getEquipCode());
            ps.setString(2, data.getEquipCode());
            ps.setTimestamp(3, Timestamp.valueOf(data.getTs()));
            ps.setObject(4, data.getDataTime());
            ps.setObject(5, data.getEn());
            ps.setObject(6, data.getUa());
            ps.setObject(7, data.getUb());
            ps.setObject(8, data.getUc());
            ps.setObject(9, data.getUab());
            ps.setObject(10, data.getUbc());
            ps.setObject(11, data.getUca());
            ps.setObject(12, data.getIa());
            ps.setObject(13, data.getIb());
            ps.setObject(14, data.getIc());
            ps.setObject(15, data.getP());
            ps.setObject(16, data.getQ());
            ps.setObject(17, data.getS());
            ps.setObject(18, data.getPf());
            ps.setObject(19, data.getF());
            ps.setObject(20, data.getRae());
            ps.setObject(21, data.getFre());
            ps.setObject(22, data.getRre());
            ps.setObject(23, data.getCd());
            ps.setObject(24, data.getMd());
        });
    }

    /**
     * 查询时间范围内的数据
     */
    public List<ElecEquipMonitorPo> queryByTimeRange(String equipCode, LocalDateTime start,
        LocalDateTime end) {
        String sql = TDengineSqlConstants.QUERY_ELEC_EQUIP_BY_TIME_RANGE;
        return jdbcTemplate.query(sql,
            new Object[]{equipCode, Timestamp.valueOf(start), Timestamp.valueOf(end)},
            new BeanPropertyRowMapper<>(ElecEquipMonitorPo.class));
    }

    /**
     * 查询最新数据
     */
    public ElecEquipMonitorPo queryLatest(String equipCode) {
        String sql = TDengineSqlConstants.QUERY_ELEC_EQUIP_LATEST;
        List<ElecEquipMonitorPo> results = jdbcTemplate.query(sql,
            new Object[]{equipCode, equipCode},
            new BeanPropertyRowMapper<>(ElecEquipMonitorPo.class));
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 聚合查询 - 按小时统计功率和电能
     */
    public List<ElecEquipHourlyStats> queryHourlyStats(String equipCode, LocalDateTime start,
        LocalDateTime end) {
        String sql = TDengineSqlConstants.QUERY_ELEC_EQUIP_HOURLY_AGG;
        return jdbcTemplate.query(sql,
            new Object[]{equipCode, Timestamp.valueOf(start), Timestamp.valueOf(end)},
            new BeanPropertyRowMapper<>(ElecEquipHourlyStats.class));
    }

    /**
     * 电气设备小时统计数据模型
     */
    @Data
    public static class ElecEquipHourlyStats {

        private LocalDateTime ts;
        private Double avgPower;
        private Double maxPower;
        private Double minPower;
        private Double totalEnergy;
        private Integer count;
    }
}
