package com.cdz360.iot.ess.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.ds.ro.GtiStringRoDs;
import com.cdz360.iot.ds.rw.GtiStringRwDs;
import com.cdz360.iot.model.pv.po.GtiStringPo;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvStringService {

    @Autowired
    private GtiStringRoDs gtiStringRoDs;

    @Autowired
    private GtiStringRwDs gtiStringRwDs;

    @Transactional
    public Mono<BaseResponse> addGtiString(List<GtiStringPo> poList) {
        boolean allMatch = poList.stream()
            .allMatch(t -> StringUtils.isNotBlank(t.getGtiDno()) && t.getStringIdx() != null);
        if (!allMatch) {
            return Mono.error(new DcArgumentException("参数错误"));
        }
        poList.forEach(t -> {
            t.setCreateTime(new Date());
            t.setEnable(true);
        });
        boolean insertOrUpdate = gtiStringRwDs.insertOrUpdate(poList);
        return insertOrUpdate ? Mono.just(RestUtils.success())
            : Mono.just(RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "新增失败"));
    }

    @Transactional
    public Mono<BaseResponse> batchEditGtiString(List<GtiStringPo> poList) {
        boolean allMatch = poList.stream()
            .allMatch(t -> StringUtils.isNotBlank(t.getGtiDno()) && t.getStringIdx() != null);
        if (!allMatch) {
            return Mono.error(new DcArgumentException("参数错误"));
        }

        // 先禁用所有组串
        String gtiDno = poList.get(0).getGtiDno();
        gtiStringRwDs.disableAllByGtiDno(gtiDno);

        // 再更新组串
        poList.forEach(t -> {
            t.setUpdateTime(new Date());
            t.setEnable(true);
        });
        boolean insertOrUpdate = gtiStringRwDs.insertOrUpdate(poList);
        return insertOrUpdate ? Mono.just(RestUtils.success())
            : Mono.just(RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "修改失败"));
    }

    @Transactional
    public Mono<BaseResponse> editGtiString(GtiStringPo po) {
        if (StringUtils.isBlank(po.getGtiDno()) || po.getStringIdx() == null) {
            return Mono.error(new DcArgumentException("参数错误"));
        }
        po.setUpdateTime(new Date());
        boolean insertOrUpdate = gtiStringRwDs.insertOrUpdateSingle(po);
        return insertOrUpdate ? Mono.just(RestUtils.success())
            : Mono.just(RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "修改失败"));
    }

    public Mono<ListResponse<GtiStringPo>> getGtiStringList(String gtiDno) {
        List<GtiStringPo> gtiStringPoList = gtiStringRoDs.getByGtiDno(gtiDno, null);
        return Mono.just(RestUtils.buildListResponse(gtiStringPoList));
    }

    public Mono<BaseResponse> deleteGti(String dno, Integer stringIdx) {
        if (StringUtils.isBlank(dno) || stringIdx == null) {
            return Mono.error(new DcArgumentException("参数错误"));
        }
        boolean disableGtiString = gtiStringRwDs.disableGtiString(dno, stringIdx);
        return disableGtiString ? Mono.just(RestUtils.success())
            : Mono.just(RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "删除失败"));
    }

}
