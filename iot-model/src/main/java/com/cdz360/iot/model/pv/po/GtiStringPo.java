package com.cdz360.iot.model.pv.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "逆变器组串")
public class GtiStringPo {

    @Schema(description = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "逆变器唯一编号")
    @NotNull(message = "gtiDno 不能为 null")
    @Size(max = 100, message = "gtiDno 长度不能超过 100")
    private String gtiDno;

    @Schema(description = "组串序号")
    @NotNull(message = "stringIdx 不能为 null")
    private Integer stringIdx;

    @Schema(description = "组串是否启用")
    private Boolean working;

    @Schema(description = "组件厂家")
    @Size(max = 32, message = "moduleVendor 长度不能超过 32")
    private String moduleVendor;

    @Schema(description = "组件型号")
    @Size(max = 32, message = "moduleModel 长度不能超过 32")
    private String moduleModel;

    @Schema(description = "组件类型")
    private Integer moduleType;

    @Schema(description = "组件数量")
    private Integer moduleNum;

    @Schema(description = "组件最大功率")
    private BigDecimal moduleMaxPower;

    @Schema(description = "组串容量")
    private Integer capacity;

    @Schema(description = "是否有效")
    private Boolean enable;

    @Schema(description = "记录创建时间")
    private Date createTime;

    @Schema(description = "记录最后修改时间")
    private Date updateTime;

}

