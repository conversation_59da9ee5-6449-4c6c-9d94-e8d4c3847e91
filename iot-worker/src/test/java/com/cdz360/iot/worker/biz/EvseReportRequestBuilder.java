package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.worker.IotWorkderBuilderBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class EvseReportRequestBuilder extends IotWorkderBuilderBase {

    private final Logger logger = LoggerFactory.getLogger(EvseReportRequestBuilder.class);

    public EvseReportRequestV2 build() {
        EvseReportRequestV2 report = new EvseReportRequestV2();
        report.setEvseStatus(EvseStatus.IDLE);
        //report.setGwno("abc1000");
        report.setEvseNo("aaa111");
        return report;
    }
}
