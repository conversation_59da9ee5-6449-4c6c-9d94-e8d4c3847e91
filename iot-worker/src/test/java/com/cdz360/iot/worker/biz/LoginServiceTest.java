package com.cdz360.iot.worker.biz;

import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.model.register.GwLoginResult;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.model.iot.param.GwLoginParam;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/1/7
 **/
public class LoginServiceTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(LoginServiceTest.class);

    @InjectMocks
    private LoginService loginService;
    @Mock
    private GwInfoService gwInfoService;

    @Test
    public void doLogin() throws NoSuchAlgorithmException {
        String gwno = "GWNO19032700004F";
        String realm = "B3FFE3D7-02DC-4EC3-8F83-114D111CEAC8";
        String token = "722080e93abfd15332802d9f58bf8c9ae110e501b99dfdd0847e8e77fd7706ea";
        String ip = "**************";
        int ver = 2;
        GwInfoDto gwInfoDto = new GwInfoDto();
        gwInfoDto.setPasscode("ncbKdPZAIxxAhfna");
        gwInfoDto.setIp(ip);
        Mockito.when(gwInfoService.getByGwno(gwno, true)).thenReturn(gwInfoDto);
        Mockito.when(gwInfoService.update(gwInfoDto)).thenReturn(true);
        GwLoginParam param = new GwLoginParam();
        param.setGwno(gwno)
                .setProtocolVer(ver)
                .setToken(token)
                .setWanIp(ip)
                .setLanIp("lanIp")
                .setMac("testMac");
        GwLoginResult result = loginService.doLogin(realm, param);
        IotAssert.isNotNull(result, "EvseRegServiceTest.doLogin() Unit Test Error");
    }

}