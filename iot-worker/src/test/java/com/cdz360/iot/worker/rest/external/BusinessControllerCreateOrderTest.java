//package com.cdz360.iot.worker.rest.external;
//
//import com.cdz360.base.model.base.constants.DcConstants;
//import com.cdz360.base.utils.JsonUtils;
//import com.cdz360.iot.model.base.BaseRpcResponse;
//import com.cdz360.iot.worker.IotWorkerTestBase;
//import com.cdz360.iot.worker.model.order.CreateOrderRequest;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.http.MediaType;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.RequestBuilder;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//
//@AutoConfigureMockMvc
//public class BusinessControllerCreateOrderTest extends IotWorkerTestBase {
//    private static final String URL_CREATE_ORDER = "/iot/biz/order/create";
//    private final Logger logger = LoggerFactory.getLogger(BusinessControllerCreateOrderTest.class);
//    @Autowired
//    private MockMvc mockMvc;
////
////    private Queue<CreateOrderRequest> paramList = new ConcurrentLinkedQueue<>();
////
////    @BeforeEach
////    public void setup() {
////        for(int i = 0; i < 1000; i ++) {
////
////        }
////    }
//
//    @Test
//    public void test_createOrderFail() throws Exception {
//        CreateOrderRequest param = new CreateOrderRequest();
//        param.setEvseId("aaa");
//
//        RequestBuilder builder = MockMvcRequestBuilders.post(URL_CREATE_ORDER)
//                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                .content(JsonUtils.toJsonString(param));
//
//        String resBody = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
//        logger.info("response = {}", resBody);
//        BaseRpcResponse res = JsonUtils.fromJson(resBody, BaseRpcResponse.class);
//        Assertions.assertNotEquals(DcConstants.KEY_RES_CODE_SUCCESS, res.getStatus());
//
//    }
//}
