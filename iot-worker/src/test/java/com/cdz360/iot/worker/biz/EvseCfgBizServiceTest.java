package com.cdz360.iot.worker.biz;

import com.cdz360.iot.model.evse.vo.PriceSchemeSiteVo;
import com.cdz360.iot.worker.IotWorkerTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Slf4j
class EvseCfgBizServiceTest extends IotWorkerTestBase {

    @Autowired
    private EvseCfgBizService evseCfgBizService;

    @Test
    void test_getPriceSchemeSiteInfo() {
        List<PriceSchemeSiteVo> priceSchemeSiteInfo = evseCfgBizService.getPriceSchemeSiteInfo(Collections.singletonList(1L));
        log.info("size = {}", priceSchemeSiteInfo.size());
    }
}