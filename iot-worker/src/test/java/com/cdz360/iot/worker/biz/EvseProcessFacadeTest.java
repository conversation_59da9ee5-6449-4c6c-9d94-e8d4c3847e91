package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.worker.IotWorkerTestBase;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/1/7
 **/
public class EvseProcessFacadeTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(EvseProcessFacadeTest.class);

    @InjectMocks
    private EvseProcessor evseProcessor;

    @Autowired
    private EvseReportRequestBuilder evseReportRequestBuilder;

    @Mock
    private EvseReportRequestV2 report;

    @BeforeEach
    public void init() {
        this.report = this.evseReportRequestBuilder.build();
    }

    @Test
    public void processEvseStatusReport() {
        // report.setSeq("1111111");
        // report.setNet(NetType.ETHENET).setPlugNum(1);
        logger.info("report = {}", report);
        Mockito.doNothing().when(evseProcessor).processEvseNormalStatusV2(report, "123456789");
        if (report.getEvseStatus() == EvseStatus.IDLE
                || report.getEvseStatus() == EvseStatus.BUSY) {
            this.evseProcessor.processEvseNormalStatusV2(report, "123456789");
        }
    }
}