package com.cdz360.iot.worker.biz;

import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.model.register.GwRegisterResult;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.IotWorkerTestMain;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = IotWorkerTestMain.class)
public class GwRegisterServiceTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(GwRegisterServiceTest.class);

    @Autowired
    GwRegisterService gwRegisterService;

    @Autowired
    private GwInfoService gwInfoService;


    @Value("${iot.gw.idle.gwno}")
    private String idleGwno;
    @Value("${iot.gw.idle.passcode}")
    private String idlePasscode;
//    @Value("${iot.gw.idle.realm}")
//    private String idleRealm;

    @Test
    public void doRegisterTest() throws InterruptedException {
        String gwMac = "FF-00-00-00-00-00";
        int ver = 2;
        GwRegisterResult res = gwRegisterService.doRegister(idleGwno, gwMac, "192.168.64.147", "117.185.89.66", ver, 0);
        GwInfoDto gwInfoDto = gwInfoService.getByGwno(res.getGwno(), false);
        logger.info("gwInfoDto: {}", gwInfoDto);
        logger.info("register", res.getGwno());
        GwRegisterResult res1 = gwRegisterService.doRegister(res.getGwno(), gwMac, "192.168.64.147", "117.185.89.66", ver, 0);
        logger.info("register", res1.getGwno());
        Assertions.assertEquals(res.getGwno(), res1.getGwno());
        Assertions.assertEquals(res.getPasscode(), res1.getPasscode());
        GwInfoDto gwInfoDto1 = gwInfoService.getByGwno(res.getGwno(), false);

        Assertions.assertNotEquals(0.0, gwInfoDto1.getLon());
        Assertions.assertNotEquals(0.0, gwInfoDto1.getLat());
        Assertions.assertNotEquals("", gwInfoDto1.getCityCode());

        logger.info("gwInfoDto1: {}", gwInfoDto1);
    }
}
