package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.common.utils.IotAssert;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.ds.rw.mapper.EvseRwQueryMapper;
import com.cdz360.iot.ds.rw.mapper.GwInfoRwQueryMapper;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.BaseSshRequest;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.cfg.CfgEvse;
import com.cdz360.iot.model.evse.cfg.CfgEvseParam;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.model.type.GwRequestMethod;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.model.type.SshStatus;
import com.cdz360.iot.worker.IotWorkerTestMain;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

//import com.cdz360.iot.worker.ds.mapper.CfgRecordMapper;
//import com.cdz360.iot.worker.ds.mapper.CfgRecordRefMapper;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/5/8
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = IotWorkerTestMain.class)
@ExtendWith(SpringExtension.class)
public class BusinessServiceTest {
    @Mock
    private GwInfoRwQueryMapper gwInfoRwQueryMapper;
    @Mock
    private MqService mqService;
//    @Mock
//    private CfgRecordMapper cfgRecordMapper;
//    @Mock
//    private CfgRecordRefMapper cfgRecordRefMapper;
    @Mock
    private EvseRwQueryMapper evseRwQueryMapper;
    @Mock
    private SequenceRwService sequenceRwService;
    @Mock
    private EvseCfgRedisService evseCfgRedisService;
    @Autowired
    private BusinessService businessService;

    @Test
    public void startGwTunnel() {
        BaseRpcResponse res = BaseRpcResponse.newInstance();
        GwInfoDto po = new GwInfoDto();
        String gwno = "GWNO19050800016C";
        String serverAddr = "***********";
        int serverPort = 22;
        int localPort = 22;
        int expire = 30;
        Mockito.when(gwInfoRwQueryMapper.getByGwnoAndEnable(gwno, false)).thenReturn(po);
        po.setGwno(gwno).setMqType(GwMqType.MQ_TYPE_RABBITMQ).setStatus(GwStatus.OFFLINE);

        int remoteTunnelPort = new Random().nextInt(10000) + 40000;
        BaseSshRequest.builder builder = new BaseSshRequest.builder();
        builder.setData(GwRequestMethod.TUNNEL, SshStatus.START, serverAddr,
                        serverPort, remoteTunnelPort,
                        localPort, expire)
                .setGwno(gwno);
        BaseSshRequest.REQ req = builder.build();
        Mockito.doNothing().when(mqService).publishMessage(po, false, req.toString());
        res = businessService.startGwTunnel(gwno, serverAddr, serverPort, localPort, expire);
        IotAssert.isTrue(res.getStatus() == DcConstants.KEY_RES_CODE_SERVICE_ERROR, "BusinessServiceTest.startGwTunnel() Unit Test Error");
    }

    @Test
    public void stopGwTunnel() {
        String gwno = "GWNO19050800016C";
        GwInfoDto po = new GwInfoDto();
        po.setGwno(gwno).setMqType(GwMqType.MQ_TYPE_RABBITMQ);
        Mockito.when(gwInfoRwQueryMapper.getByGwnoAndEnable(gwno, false)).thenReturn(po);
        Mockito.doNothing().when(mqService).publishMessage(po, false, "");
        BaseRpcResponse res = businessService.stopGwTunnel(gwno);
        IotAssert.isTrue(res.getStatus() != DcConstants.KEY_RES_CODE_SERVICE_ERROR, "BusinessServiceTest.stopGwTunnel() Unit Test Error");
    }

    @Test
    public void modifyEvseCfg() {
        String json = "{\"cfgEvse\":{\"adminCode\":\"225008\",\"cfgVer\":\"3322211\",\"charge\":{\"defaultCode\":1,\"priceSchemaList\":[{\"code\":1,\"elecPrice\":23456},{\"code\":254,\"elecPrice\":23456},{\"code\":255,\"elecPrice\":23456}],\"servPrice\":2,\"timeList\":[{\"code\":255,\"startTime\":\"12:00\",\"stopTime\":\"13:00\"},{\"code\":254,\"startTime\":\"13:00\",\"stopTime\":\"18:00\"},{\"code\":254,\"startTime\":\"18:00\",\"stopTime\":\"24:00\"}]},\"qrUrl\":\"http://wechat.iot.dingchong.com/pl/0102019040412301\",\"vin\":true,\"whiteCards\":[{\"cardNumber\":\"77777777\",\"passcode\":\"1234567\"}]},\"evseIds\":[\"01020190428000\"]}";
        CfgEvseParam cfgEvseParam = JsonUtils.fromJson(json, CfgEvseParam.class);
        EvsePo po = new EvsePo();
        po.setEvseId("01020190428000").setGwno("abc123");
        List<EvsePo> evsePoList = new ArrayList<EvsePo>();
        evsePoList.add(po);
        Mockito.when(evseRwQueryMapper.getEvsePos(cfgEvseParam.getEvseIds(), false)).thenReturn(evsePoList);
        List<String> gwnos = new ArrayList<String>();
        gwnos.add("abc123");
        List<GwInfoDto> gwInfoDtoList = new ArrayList<GwInfoDto>();
        GwInfoDto gwInfoDto = new GwInfoDto();
        gwInfoDto.setGwno("abc123").setMqType(GwMqType.MQ_TYPE_RABBITMQ);
        gwInfoDtoList.add(gwInfoDto);
        Mockito.when(gwInfoRwQueryMapper.getGwList(gwnos, GwStatus.NORMAL)).thenReturn(gwInfoDtoList);
        Mockito.when(sequenceRwService.getNextCfgVer()).thenReturn("3322211");
//        Mockito.when(cfgRecordMapper.insert(new CfgRecordPo())).thenReturn(1);
//        Mockito.doNothing().when(cfgRecordRefMapper).batchInsert(new ArrayList<CfgRecordRefPo>());
        Mockito.doNothing().when(evseCfgRedisService).append("", new CfgEvse());
        Mockito.when(sequenceRwService.getNextOutRequestSeq()).thenReturn("1111");
        Mockito.doNothing().when(mqService).publishMessage(gwInfoDto, false, "");
        BaseRpcResponse res = businessService.modifyEvseCfg(cfgEvseParam);
        IotAssert.isTrue(res.getStatus() == 0, "BusinessServiceTest.modifyEvseCfg() Unit Test Error");
    }

    /*@Test
    public void evseCfgResult() {
        String evseId = "***********";
        String cfgVer = "***********";
        String result = "SUCCESS";
        Mockito.when(cfgRecordMapper.countByCfgVer(cfgVer)).thenReturn(0);
        Mockito.when(cfgRecordMapper.getNumByResultAndCfgVer(result, cfgVer)).thenReturn(1);
        Mockito.when(cfgRecordMapper.modifyByResultAndCfgVer(result, cfgVer, 2)).thenReturn(1);
        Mockito.when(cfgRecordMapper.getRecordIdByCfgVer(cfgVer)).thenReturn(1);
        Mockito.when(cfgRecordRefMapper.modifyResultByRecordIdAndEvseId(result, 1, evseId)).thenReturn(1);
    }*/

    @Test
    public void test_rebootEvse() {
        this.businessService.rebootEvse("016666666666");
    }

    @Test
    void evseCfgResult() {
        String req = "{\"evseNo\":\"019045920000\",\"cfgVer\":\"CVER200622002A68\",\"result\":\"FAIL\",\"adminCodeResult\":null,\"triggerResult\":null,\"whiteCardsResult\":null,\"chargeResult\":null,\"qrResult\":null}";
        businessService.evseCfgResult(JsonUtils.fromJson(req, CfgEvseResultReqV2.class), "");
    }
}