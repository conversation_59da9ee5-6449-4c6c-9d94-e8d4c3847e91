package com.cdz360.iot.worker.biz.evseCfgCheck;

import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.biz.GwInfoServiceTest;
import com.cdz360.iot.worker.model.iot.dto.EvseCfgCheckDto;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

class EvseCfgCheckHandleTest extends IotWorkerTestBase {

    private final Logger logger = LoggerFactory.getLogger(GwInfoServiceTest.class);

    @Autowired
    private EvseCfgCheckHandle evseCfgCheckHandle;


    @Test
    void check() {
        String traceId = UUID.randomUUID().toString();
        String evseNo = "020200415000";
        Optional<EvseCfgCheckDto> optional = evseCfgCheckHandle.preCheck(traceId, evseNo);
        optional.ifPresent(e -> {
            evseCfgCheckHandle.check(traceId, evseNo, e);
        });
    }
}