package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.data.cache.model.IotGwCmdCacheVo;
import com.cdz360.iot.biz.MqService;
import com.cdz360.iot.model.evse.upgrade.EvseVersion;
import com.cdz360.iot.model.gw.MqDebugEvseCmd;
import com.cdz360.iot.model.gw.MqRebootEvseCmd;
import com.cdz360.iot.model.gw.MqUpgradeEvseCmd;
import com.cdz360.iot.model.order.type.EvseDebugMethod;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.type.GwMqType;
import com.cdz360.iot.worker.IotWorkerTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
public class MqttServiceTest extends IotWorkerTestBase {
    private String gwno = "GWNO19083100034D";
    private String evseNo = "016666666666";

    @Autowired
    private MqService mqService;


    /**
     * 发送桩重启的下行指令
     */
    @Test
    public void test_publish_rebootEvse() {

//        String str = "{\"seq\":\"69833319215063040\",\"data\":{\"evseNo\":\"016666666666\",\"protocolVer\":35,\"power\":0,\"supplyType\":\"DC\",\"netType\":\"M4G\",\"plugNum\":2,\"passcodeVer\":0,\"pcVer\":[{\"name\":\"PC01\",\"vendorCode\":\"0\",\"swVer\":\"37779\",\"hwVer\":\"0\"},{\"name\":\"PC02\",\"vendorCode\":\"0\",\"swVer\":\"37779\",\"hwVer\":\"0\"},{\"name\":\"PC03\",\"vendorCode\":\"0\",\"swVer\":\"37779\",\"hwVer\":\"0\"}],\"plugs\":[{\"plugId\":1,\"power\":null,\"voltage\":{\"min\":null,\"max\":null},\"current\":{\"min\":0.0,\"max\":10.0}},{\"plugId\":2,\"power\":null,\"voltage\":{\"min\":null,\"max\":null},\"current\":{\"min\":0.0,\"max\":10.0}}]}}";
//        GwObjReqMsg<EvseResiterReqV2> reqX = JsonUtils.fromJson(str, new TypeReference<GwObjReqMsg<EvseResiterReqV2>>() {
//        });
//
//        log.info("reqX = {}", reqX);


        MqRebootEvseCmd req = new MqRebootEvseCmd();
        req.setEvseNo(evseNo);
        req.setTaskNo(UUID.randomUUID().toString());
        IotGwCmdCacheVo<MqRebootEvseCmd> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(gwno)
                .setSeq(UUID.randomUUID().toString())
                .setCmd(IotGwCmdType2.CE_REBOOT)
                .setEvseNo(evseNo)
                .setPlugId(0)
                .setData(req);
        GwInfoDto gwInfo = new GwInfoDto();
        gwInfo.setGwno("abc123").setMqType(GwMqType.MQ_TYPE_RABBITMQ);
        this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
    }


    /**
     * 发送桩debug指令
     */
    @Test
    public void test_evseDebug() {
        MqDebugEvseCmd req = new MqDebugEvseCmd();
        req.setEvseNo(evseNo);
        req.setDebugMethod(EvseDebugMethod.ON);
        req.setMsg("ACDE0123");
        IotGwCmdCacheVo<MqDebugEvseCmd> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(gwno)
                .setSeq(UUID.randomUUID().toString())
                .setCmd(IotGwCmdType2.CE_DEBUG)
                .setEvseNo(evseNo)
                .setPlugId(0)
                .setData(req);
        GwInfoDto gwInfo = new GwInfoDto();
        gwInfo.setGwno("abc123").setMqType(GwMqType.MQ_TYPE_RABBITMQ);
        this.mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));
    }

    /**
     * 桩升级的MQ指令
     */
    @Test
    public void test_evseUpgrade() {
        List<EvseVersion> bundleList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            EvseVersion bundle = new EvseVersion();
            bundle.setName("PC0" + (i + 1));
            bundle.setVendorCode(123);
            bundle.setSwVer(345);
            bundle.setUrl("FTP://192.168.64.3/abc.bin");
            bundleList.add(bundle);
        }
        MqUpgradeEvseCmd req = new MqUpgradeEvseCmd();
        req.setDownloadType("FTP");
        req.setDownloadUsername("aaabbb");
        req.setDownloadPasscode("12345678");
        req.setEvseNo(evseNo);
        req.setTaskNo(UUID.randomUUID().toString());
        req.setEvseBundle(bundleList);
        IotGwCmdCacheVo<MqUpgradeEvseCmd> cmd = new IotGwCmdCacheVo<>();
        cmd.setGwno(gwno)
                .setSeq(UUID.randomUUID().toString())
                .setCmd(IotGwCmdType2.CE_UPGRADE)
                .setPlugId(0)
                .setData(req);
        cmd.setEvseNo(evseNo);
        cmd.setSeq(UUID.randomUUID().toString());

        GwInfoDto gwInfo = new GwInfoDto();
        gwInfo.setGwno("abc123").setMqType(GwMqType.MQ_TYPE_RABBITMQ);
        mqService.publishMessage(gwInfo, false, JsonUtils.toJsonString(cmd));

    }
}
