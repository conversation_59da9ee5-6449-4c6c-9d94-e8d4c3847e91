package com.cdz360.iot.worker.rest.external;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.model.evse.EvseReportRequestV2;
import com.cdz360.iot.model.gw.GwObjReqMsg;
import com.cdz360.iot.worker.IotWorkerMockTestBase;
import com.cdz360.iot.worker.model.gw.EvseResiterReqV2;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class IotGwControllerV2Test extends IotWorkerMockTestBase {

    // 网关编号
    private static final String GW_NO = "GWNO200309000035";

    // 从日志中获取的 token 值
    private static final String GW_TOKEN = "Basic de1ffa007111c2783969c1cb579f0cb57976768cd4837e59cc375ff1065cf0da";

    @BeforeEach
    public void init() {
        // 模拟网关登录操作, 生成 token

    }

    @Test
    void test_evseRegister() throws Exception {
        String url = null;

        String json = "{\"seq\":\"143098076570902528\",\"data\":{\"evseNo\":\"020200518000\",\"protocolVer\":350,\"power\":0,\"supplyType\":\"DC\",\"netType\":\"M4G\",\"plugNum\":2,\"passcodeVer\":0,\"pcVer\":[{\"name\":\"PC01\",\"vendorCode\":\"4\",\"swVer\":\"151\",\"hwVer\":\"11\"},{\"name\":\"PC02\",\"vendorCode\":\"0\",\"swVer\":\"105\",\"hwVer\":\"11\"},{\"name\":\"PC03\",\"vendorCode\":\"0\",\"swVer\":\"32\",\"hwVer\":\"12\"}],\"plugs\":[{\"plugId\":1,\"voltage\":{},\"current\":{\"min\":0.0,\"max\":10.0}},{\"plugId\":2,\"voltage\":{},\"current\":{\"min\":0.0,\"max\":10.0}}]}}";
        GwObjReqMsg<EvseResiterReqV2> request = JsonUtils.fromJson(json, new TypeReference<>() {});
        request.setSeq(UUID.randomUUID().toString());
        request.setVer(2);
        request.setGwno(GW_NO);
        request.getData().setEvseNo("020200518000");

        String req = JsonUtils.toJsonString(request); // 请求体
        log.info("req param = {}", req);

        // 查询是否存在
        url = "/iot/evse/register";
        mockMvc.perform(buildPost(url, req))
                .andExpect(handler().handlerType(IotGwControllerV2.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("evseRegister")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();

    }


    @Test
    void test_evseStatus() throws Exception {
        String url = null;

        String json = "{\"seq\":\"143380259667763200\",\"data\":{\"evseNo\":\"026666600000\",\"evseStatus\":\"IDLE\",\"temp\":20,\"errorCode\":0,\"error\":\"\",\"plugs\":[{\"plugId\":1,\"plugStatus\":\"IDLE\",\"errorCode\":null,\"error\":null,\"alertCode\":null,\"temp\":null},{\"plugId\":2,\"plugStatus\":\"IDLE\",\"errorCode\":null,\"error\":null,\"alertCode\":null,\"temp\":null},{\"plugId\":3,\"plugStatus\":\"IDLE\",\"errorCode\":null,\"error\":null,\"alertCode\":null,\"temp\":null},{\"plugId\":4,\"plugStatus\":\"IDLE\",\"errorCode\":null,\"error\":null,\"alertCode\":null,\"temp\":null}]},\"v\":null,\"n\":null}";
        GwObjReqMsg<EvseReportRequestV2> request = JsonUtils.fromJson(json, new TypeReference<>() {});
        request.setSeq(UUID.randomUUID().toString());
        request.setVer(2);
        request.setGwno(GW_NO);

        String req = JsonUtils.toJsonString(request); // 请求体
        log.info("req param = {}", req);

        // 查询是否存在
        url = "/iot/evse/status";
        mockMvc.perform(buildPost(url, req))
                .andExpect(handler().handlerType(IotGwControllerV2.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("evseStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    private MockHttpServletRequestBuilder buildPost(String url, String body) {
        return post(url + "?v=2&n=" + GW_NO)
                .header("Authorization", GW_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(body);
    }

    private MockHttpServletRequestBuilder buildGet(String url) {
        return get(url + "?v=2&n=" + GW_NO)
                .header("Authorization", GW_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON);
    }
}