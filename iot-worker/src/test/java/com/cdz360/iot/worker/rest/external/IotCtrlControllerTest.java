package com.cdz360.iot.worker.rest.external;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.ds.rw.SequenceRwService;
import com.cdz360.iot.model.site.ctrl.SiteCtrlInfo;
import com.cdz360.iot.model.site.ctrl.SiteCtrlMonitor;
import com.cdz360.iot.model.site.ctrl.SiteCtrlReq;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.utils.CustomerTestUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class IotCtrlControllerTest extends IotWorkerTestBase {

    private static final SiteCtrlReq<SiteCtrlMonitor> TEST_MONITOR_REQ = new SiteCtrlReq<>();
    private static final List<Integer> LOAD_RATIO_LIST = List.of(10);
    private static final List<Integer> PWR_TEMP_LIST = List.of(10);

    private static final String CTRL_NO = "CE_123456789";
    private static final Integer CTRL_VER = 10;

    private static final SiteCtrlReq<SiteCtrlInfo> TEST_CTRL_INFO_REQ = new SiteCtrlReq<>();

    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext wac;

    @Autowired
    private SequenceRwService sequenceRwService;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();

        // 监控数据初始化
        SiteCtrlMonitor monitor = new SiteCtrlMonitor();
        monitor.setLoadRatio(LOAD_RATIO_LIST)
                .setPwrTemp(PWR_TEMP_LIST);
        TEST_MONITOR_REQ.setData(monitor);
        TEST_MONITOR_REQ.setCtrlNo(CTRL_NO);
        TEST_MONITOR_REQ.setVer(CTRL_VER);
        TEST_MONITOR_REQ.setSeq(sequenceRwService.getNextOutRequestSeq());

        // 上报配置数据初始化
        SiteCtrlInfo info = CustomerTestUtils.autoInit(SiteCtrlInfo.class);
        TEST_CTRL_INFO_REQ.setData(info);
        TEST_CTRL_INFO_REQ.setCtrlNo(CTRL_NO);
        TEST_CTRL_INFO_REQ.setVer(CTRL_VER);
        TEST_CTRL_INFO_REQ.setSeq(sequenceRwService.getNextOutRequestSeq());
    }

    @Test
    void test_monitor() throws Exception {

        MockHttpServletRequestBuilder rb = null;
        String url = null;

        String req = JsonUtils.toJsonString(TEST_MONITOR_REQ); // 请求体
        log.info("req param = {}", req);

        // 查询是否存在
        url = "/iot/ctrl/monitor";
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(req);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(IotCtrlController.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("monitor")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }

    @Test
    void test_cfgInfo() throws Exception {
        MockHttpServletRequestBuilder rb = null;
        String url = null;

        String req = JsonUtils.toJsonString(TEST_CTRL_INFO_REQ); // 请求体
        log.info("req param = {}", req);

        // 查询是否存在
        url = "/iot/ctrl/cfg/info";
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(req);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(IotCtrlController.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("cfgInfo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();

        // 检查redis是否存在
    }
}