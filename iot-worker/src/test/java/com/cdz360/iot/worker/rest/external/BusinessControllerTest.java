package com.cdz360.iot.worker.rest.external;

import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.evse.cfg.*;
import com.cdz360.iot.model.type.CfgEvseResultType;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.biz.BusinessService;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReq;
import com.cdz360.iot.worker.model.gw.CfgEvseResultReqV2;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BusinessControllerTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(BusinessControllerTest.class);
    @Autowired
    private BusinessService businessService;

//    @Autowired
//    private DzDeviceManagerFeignClient deviceManagerFeignClient;


//    public BaseRpcResponse startGwTunnel(@Parameter(name = "网关编号.") @RequestParam(required = false) String gwno,
//                                         @Parameter(name = "ssh跳板机地址") @RequestParam String serverAddr,
//                                         @Parameter(name = "ssh跳板机端口") @RequestParam int serverPort,
//                                         @Parameter(name = "有效期, 单位分钟") @RequestParam(required = false, defaultValue = "60") int expire) {

    @Test
    public void evseStatus() {
        List<CfgEvseAll> cfgList = new ArrayList<CfgEvseAll>();
        CfgEvseAll cfgEvseAll = new CfgEvseAll();
        cfgEvseAll.setAdminCodeA("testA");
        cfgEvseAll.setAdminCodeB("testB");
        cfgEvseAll.setAutoStop(true);
        cfgEvseAll.setBalanceMode(EvseCfgEnum.AUTO);
        cfgEvseAll.setBatteryCheck(true);
        cfgEvseAll.setCardCharge(true);
        Charge charge = new Charge();
        charge.setChargeId(32L);
        charge.setDefaultCode(1);
        charge.setServPrice(2L);
        PriceSchema priceSchema = new PriceSchema();
        priceSchema.setCode(55);
        priceSchema.setElecPrice(22);

        List<PriceSchema> priceSchemaList = new ArrayList<PriceSchema>();
        priceSchemaList.add(priceSchema);
        CfgTime cfgTime = new CfgTime();
        cfgTime.setCode(223);
        cfgTime.setStartTime("1080");
        cfgTime.setStopTime("1440");
        List<CfgTime> cfgTimes = new ArrayList<CfgTime>();
        charge.setTimeList(cfgTimes);
        charge.setPriceSchemaList(priceSchemaList);
        cfgTimes.add(cfgTime);
        cfgEvseAll.setCharge(charge);
        cfgEvseAll.setCombination(true);
        cfgEvseAll.setDayVolume(5);
        cfgEvseAll.setBmsVer(EvseCfgEnum.V2011);
        cfgEvseAll.setEvseId("012271000101");
        cfgEvseAll.setNightVolume(43);
        cfgEvseAll.setVin(true);
        cfgEvseAll.setQrUrl("http://124124");
        ChargeStopMode stopMode = new ChargeStopMode();
        stopMode.setAmount(true);
        stopMode.setKwh(true);
        stopMode.setTime(true);
        cfgEvseAll.setStopMode(stopMode);
        WhiteCard whiteCard = new WhiteCard();
        whiteCard.setCardNumber("34234");
        whiteCard.setPasscode("1234234");
        List<WhiteCard> whiteCards = new ArrayList<WhiteCard>();
        whiteCards.add(whiteCard);
        cfgEvseAll.setWhiteCards(whiteCards);
        cfgEvseAll.setIsolation(EvseCfgEnum.HEATING);
        System.out.println(cfgEvseAll.toJsonString());
        businessService.evseCfgInfo(cfgList);
    }

    @Test
    public void startGwTunnel() {
        System.out.println(">> gwno = {}, serverAddr = {}, serverPort = {}, expire = {}");
        BaseRpcResponse res = businessService.startGwTunnel("abc100", "192.168.64.100", 322, 22, 60);
        System.out.println(res.getStatus());
        System.out.println(res.getError());
        System.out.println("<<");
    }

    @Test
    public void evseCfgResult() {
        logger.info("=======================================start==========================================");
        logger.info(">> evseId = {}, cfgVer = {}, serverPort = {}", 15, "CVER19040900000D", "SUCCESS");

        CfgEvseResultReq req = new CfgEvseResultReq();
        req.setEvseId("012271003105");
        req.setResult(CfgEvseResultType.SUCCESS);
        req.setCfgVer("CVER19040900000D");
        req.setWhiteCardsResult(1);

        CfgEvseResultReqV2 cfgEvseResultReqV2 = new CfgEvseResultReqV2();
        cfgEvseResultReqV2.setAdminCodeResult(req.getAdminCodeResult());
        cfgEvseResultReqV2.setEvseNo(req.getEvseId());
        cfgEvseResultReqV2.setResult(req.getResult());
        cfgEvseResultReqV2.setCfgVer(req.getCfgVer());
        cfgEvseResultReqV2.setWhiteCardsResult(req.getWhiteCardsResult());

        businessService.evseCfgResult(cfgEvseResultReqV2, "aaaaaa");
        logger.info("<<");
        logger.info("========================================end==========================================");
    }

    @Test
    public void test() throws ParseException {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        Date date = simpleDateFormat.parse("01:00");
        System.out.println(date.getTime());
        long ts = date.getTime();
        System.out.println(ts);

        Date date3 = simpleDateFormat.parse("02:00");
        System.out.println(date3.getTime());
        long ts3 = date3.getTime();
        System.out.println(ts3);

        System.out.println(ts > ts3);

//        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("HH:mm");
//        long lt = new Long(res);
//        Date date2 = new Date(lt);
//        System.out.println(date2);
//        res = simpleDateFormat.format(date);
//        System.out.println(res);
    }
}
