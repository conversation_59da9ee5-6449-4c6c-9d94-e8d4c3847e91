package com.cdz360.iot.worker.utils;

import com.cdz360.iot.model.site.ctrl.PowerCtrlLmt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

import static java.lang.Math.random;

@Slf4j
public class CustomerTestUtils {

    public static <T> T autoInit(Class<T> type) throws Exception {
        // 重新实例化一个与传过来的对象类型一样的对象(支持无参构造的实例化)
        T result = type.getDeclaredConstructor().newInstance();

        // 遍历初始化对象属性值
        Field[] fields = type.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            Object value = generateVal(field);

            // 获得属性的首字母并转换为大写，与setXXX对应
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String setMethodName = "set" + firstLetter + fieldName.substring(1);

            Method setMethod = type.getMethod(setMethodName, field.getType());
            setMethod.invoke(result, value); //调用对象的setXXX方法
        }

        return result;
    }

    public static <T> T autoInit(Class<T> type, String... ignoreName) throws Exception {
        // 重新实例化一个与传过来的对象类型一样的对象(支持无参构造的实例化)
        T result = type.getDeclaredConstructor().newInstance();

        // 遍历初始化对象属性值
        Field[] fields = type.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            Object value = generateVal(field);

            // 获得属性的首字母并转换为大写，与setXXX对应
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String setMethodName = "set" + firstLetter + fieldName.substring(1);

            Method setMethod = type.getMethod(setMethodName, field.getType());
            setMethod.invoke(result, value); //调用对象的setXXX方法
        }

        return result;
    }

    public static <T> T autoInit(Class<T> type, Class<Object>... ignoreType) throws Exception {
        // 重新实例化一个与传过来的对象类型一样的对象(支持无参构造的实例化)
        T result = type.getDeclaredConstructor().newInstance();

        // 遍历初始化对象属性值
        Field[] fields = type.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            Object value = generateVal(field);

            // 获得属性的首字母并转换为大写，与setXXX对应
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String setMethodName = "set" + firstLetter + fieldName.substring(1);

            Method setMethod = type.getMethod(setMethodName, field.getType());
            setMethod.invoke(result, value); //调用对象的setXXX方法
        }

        return result;
    }

    public static Object generateVal(Field field) {

        if (field.getType().equals(String.class)) {
            return RandomStringUtils.random(10);
        }

        if (field.getType().equals(Boolean.class) ||
                field.getType().equals(boolean.class)) {
            int i = (int) (random() * 2);
            return i == 1 ? Boolean.TRUE : Boolean.FALSE;
        }

        if (field.getType().equals(Integer.class) ||
                field.getType().equals(int.class)) {
            return (int) (random() * 1000);
        }

        if (field.getType().equals(Long.class) ||
                field.getType().equals(long.class)) {
            return (long) (random() * 100000);
        }

        if (field.getType().equals(Date.class)) {
            return new Date();
        }

        if (field.getType().equals(BigDecimal.class)) {
            return BigDecimal.TEN;
        }

        // ignore list
        if (field.getType().equals(PowerCtrlLmt.class)) {
            return null;
        }

        if (field.getType().equals(ArrayList.class)) {
            return new ArrayList<>();
        }

        log.error("field: name = {}, type = {}", field.getName(), field.getType());
        throw new RuntimeException("还未对该类型进行初始化处理，请调整逻辑后在测试");
    }
}
