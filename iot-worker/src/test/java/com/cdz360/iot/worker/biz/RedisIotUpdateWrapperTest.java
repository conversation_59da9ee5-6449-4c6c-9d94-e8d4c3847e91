package com.cdz360.iot.worker.biz;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.iot.model.evse.EvsePo;
import com.cdz360.iot.model.evse.po.PlugPo;
import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.ds.service.PlugService;
import com.cdz360.iot.worker.model.iot.UpdatePlugCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class RedisIotUpdateWrapperTest extends IotWorkerTestBase {

    @Autowired
    private RedisIotUpdateWrapper redisIotUpdateWrapper;
    @Autowired
    private RedisIotRwService redisIotRwService;

    @Autowired
    private PlugService plugService;

    // @Autowired
    // private RedisTemplate redisTemplate;

    private final List<Thread> thList = new ArrayList<>();

    @Test
    public void test_buildRedisCache() {
        long startTime = System.nanoTime();
        this.redisIotUpdateWrapper.buildRedisCache(null);
        long endTime = System.nanoTime();
        log.info("total time = {}", (endTime - startTime) / 1000000);
    }


    @Test
    public void test_redisPerformance() throws InterruptedException {
        List<List<PlugPo>> plugList = new ArrayList<>();
        long start = 0;
        int size = 50;
        List<PlugPo> list = null;
        do {
            list = this.plugService.listPlug(null, null, start, size, false);
            plugList.add(list);
            start = start + list.size();
        } while (CollectionUtils.isNotEmpty(list));

        int THREAD_NUM = plugList.size();
        log.warn("THREAD_NUM = {}", THREAD_NUM);

        for (int i = 0; i < THREAD_NUM; i++) {
            this.initThreads(i, plugList.get(i));
        }

        long startTime = System.nanoTime();
        plugList.parallelStream().forEach(pl -> {
            pl.forEach(p -> {
                redisIotRwService.deletePlugRedisCache(p.getEvseId(), p.getPlugId());
            });
        });

        long endTime = System.nanoTime();
        log.error("after delete. time = {}, size = {}",
                (endTime - startTime) / 1000000, plugList.size());


        thList.forEach(Thread::start);
        //long startedTime = System.nanoTime();
        for (int i = 0; i < THREAD_NUM; i++) {
            thList.get(i).join();
        }
//        endTime = System.nanoTime();
//        log.error("startTime = {}, total = {}",
//                (startedTime - startTime) / 1000000,
//                (endTime - startTime) / 1000000);


//        plugList.stream().parallel().forEach(p -> {
//            task(p);
//        });

        endTime = System.nanoTime();
        log.error("thread end. time = {}, size = {}",
                (endTime - startTime) / 1000000, plugList.size());
    }


    private void task(PlugPo p) {
        EvsePo evse = new EvsePo();
        redisIotRwService.getPlugRedisCache(p.getEvseId(), p.getPlugId());
        UpdatePlugCacheDto dto = new UpdatePlugCacheDto();
        dto.setEvent(IotEvent.STATE_CHANGE)
                .setPlugReport(null)
                .setSite(null)
                .setEvse(evse)
                .setPlug(p);
        this.redisIotUpdateWrapper.updateRedisPlugCache(dto);
        redisIotRwService.getPlugRedisCache(p.getEvseId(), p.getPlugId());

//        redisTemplate.opsForValue().set(p.getEvseId() + p.getPlugId(), p.getEvseId(), 100, TimeUnit.SECONDS);
//        redisTemplate.opsForValue().get(p.getEvseId() + p.getPlugId());
//        redisTemplate.opsForValue().get(p.getEvseId() + p.getPlugId());
//        redisTemplate.opsForValue().get(p.getEvseId() + p.getPlugId());
//        redisTemplate.opsForValue().get(p.getEvseId() + p.getPlugId());
    }

    private void initThreads(final int id, List<PlugPo> plugList) {
        Thread th = new Thread(new Runnable() {

            @Override
            public void run() {
                long startTime = System.nanoTime();
                log.error("thread-{} start", id);
                plugList.forEach(p -> {
                    task(p);
                });

                long endTime = System.nanoTime();
                log.error("thread-{} end. time = {}", id, (endTime - startTime) / 1000000);
            }
        });

        thList.add(th);
    }
}
