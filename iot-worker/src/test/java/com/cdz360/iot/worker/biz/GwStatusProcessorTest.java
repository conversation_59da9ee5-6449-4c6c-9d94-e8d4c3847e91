package com.cdz360.iot.worker.biz;

import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.model.site.param.GwParam;
import com.cdz360.iot.model.type.GwStatus;
import com.cdz360.iot.worker.IotWorkerTestBase;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class GwStatusProcessorTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(GwStatusProcessorTest.class);

    @Autowired
    private GwStatusProcessor gwStatusProcessor;

    @Autowired
    private GwInfoService gwInfoService;


    @BeforeEach
    public void init() {
    }


    @Test
    public void updateGwStatus() {
        GwParam param = new GwParam();
        gwStatusProcessor.updateGwStatus(param);
    }

    @Test
    public void updateGwInfo() {
        String gwno = "GWNO190321000010";
        gwInfoService.updateStatus(gwno, GwStatus.NORMAL);
        GwInfoDto gwInfoDto = gwInfoService.getByGwno(gwno, true);
        gwInfoService.updateStatus(gwno, GwStatus.OFFLINE);
        GwInfoDto gwInfoDto1 = gwInfoService.getByGwno(gwno, true);
        Assertions.assertNotEquals(gwInfoDto.getStatus(), gwInfoDto1.getStatus());
    }
}
