package com.cdz360.iot.worker.biz.evseCfgCheck;

import com.cdz360.iot.worker.IotWorkerTestBase;
import com.cdz360.iot.worker.model.iot.DelayedEvse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.concurrent.DelayQueue;

@Slf4j
public class DelayedQueueConsumerTest extends IotWorkerTestBase {

    private EvseCfgCheckHandle evseCfgCheckHandle = new EvseCfgCheckHandle();

    @Test
    void consume() {
        log.info(">>");
        DelayQueue<DelayedEvse> delayQueue = new DelayQueue<>();
        for(long i = 0; i < 5; i ++) {
            DelayedEvse de = new DelayedEvse("xxxx-" + i, i);
            delayQueue.add(de);
        }
        DelayedQueueConsumer consumer = new DelayedQueueConsumer(delayQueue, evseCfgCheckHandle);
        consumer.run();
    }
}
