//package com.cdz360.iot.worker.biz;
//
//import com.cdz360.base.model.base.type.ChargeOrderStatus;
//import com.cdz360.base.model.base.type.SupplyType;
//import com.cdz360.base.model.iot.vo.PlugVo;
//import com.cdz360.iot.common.utils.IotAssert;
//import com.cdz360.iot.ds.rw.SequenceRwService;
//import com.cdz360.iot.model.evse.EvsePo;
//import com.cdz360.iot.model.evse.OrderStopRequest;
//import com.cdz360.iot.worker.IotWorkerTestBase;
//import com.cdz360.iot.worker.ds.service.EvseService;
//import com.cdz360.iot.worker.ds.service.OrderService;
//import com.cdz360.iot.model.evse.po.PlugPo;
//import com.cdz360.iot.worker.model.order.CreateOrderRequest;
//import com.cdz360.iot.worker.model.order.OrderPo;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.data.util.Pair;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.UUID;
//
///**
// * <AUTHOR>
// * @Description
// * @Date 2019/1/7
// **/
//public class OrderBizServiceTest extends IotWorkerTestBase {
//    private final Logger logger = LoggerFactory.getLogger(OrderBizServiceTest.class);
//
//    @InjectMocks
//    private OrderBizService orderBizService;
//    @Mock
//    private OrderService orderService;
//
//
//    private CreateOrderRequest orderRequest;
//
//    @Mock
//    private EvseService evseService;
//    @Mock
//    private SequenceRwService sequenceRwService;
//    @Mock
//    private MqService mqService;
//
//    @BeforeEach
//    public void init() {
//        this.orderRequest = new CreateOrderRequest() {{
//            setOrderNo("yh111");
//            setEvseId("012279999299");
////            setCarNo("11");
//            setPlugId(1);
//            setAmount(new BigDecimal(9999));
//        }};
//    }
//
//    @Test
//    public void tagOrderStop() {
//        OrderStopRequest orderReport = new OrderStopRequest() {{
//            setOrderNo("20181300000011119");
////            setEvent(GwOrderEvent.STOP);
//            setPlugId(orderRequest.getPlugId());
//            setEvseId(orderRequest.getEvseId());
//            setGwno("aaaaaaaa");
//            setStartTime(new Date());
//        }};
//        orderService.tagOrderStop(orderReport);
//    }
//
//    @Test
//    public void createOrder() {
//        EvsePo po = new EvsePo();
//        po.setGwno("abc9999").setSupply(SupplyType.BOTH);
//        //Mockito.when(orderService.getByOrderNo(orderRequest.getOrderNo(),false)).thenReturn(null);
//        Mockito.when(evseService.getEvsePo(orderRequest.getEvseId(), false)).thenReturn(po);
//        Mockito.when(evseService.getPlugPo(orderRequest.getEvseId(), orderRequest.getPlugId(), false)).thenReturn(new PlugPo());
//        Mockito.when(sequenceRwService.getNextOutRequestSeq()).thenReturn("");
//        Mockito.doNothing().when(mqService).publishMessage("", "");
//        PlugVo plugCache = new PlugVo();
//        plugCache.setEvseNo(orderRequest.getEvseId()).setIdx(orderRequest.getPlugId());
//        String gwno = orderBizService.createOrder(orderRequest);
//        logger.info("gwno = {}", gwno);
//        IotAssert.isNotNull(gwno, "OrderBizServiceTest.createOrder() Unit Test Error");
//    }
//
//    @Test
//    public void stopOrder() {
//        OrderPo po = new OrderPo();
//        po.setGwno("abc123");
//        po.setStatus(ChargeOrderStatus.START).setEvseId("111111").setPlugId(1);
//        // Mockito.when(orderService.getByOrderNo(orderRequest.getOrderNo(),true)).thenReturn(po);
//        EvsePo po1 = new EvsePo();
//        po1.setSupply(SupplyType.BOTH);
//        Mockito.when(evseService.getEvsePo(po.getEvseId(), false)).thenReturn(po1);
//        //Mockito.when(orderService.update(po)).thenReturn(true);
//        Mockito.when(sequenceRwService.getNextOutRequestSeq()).thenReturn("");
//        Mockito.doNothing().when(mqService).publishMessage("", "");
//        String gwno = orderBizService.stopOrder(po.getEvseId(), po.getPlugId(), orderRequest.getOrderNo());
//        logger.info("gwno = {}", gwno);
//        IotAssert.isNotNull(gwno, "OrderBizServiceTest.stopOrder() Unit Test Error");
//    }
//
//
//    //@Test
//    public void test_createOrder() throws InterruptedException {
//        long startTime = System.currentTimeMillis();    // debug 性能问题
//        List<Thread> thList = new ArrayList<>();
//        List<Pair<String, String>> evseList = new ArrayList<>();
//        int thNum = 40;
//
//        for (int i = 0; i < thNum; i++) {
//            String key = "10";
//            if (i < 10) {
//                key = key + "0" + i;
//            } else {
//                key = key + i;
//            }
//            evseList.add(Pair.of("abc" + key, "01227" + key + "101"));
//        }
//
//        for (int i = 0; i < thNum; i++) {
//            Thread th = this.createOrderTask(evseList.get(i).getSecond());
//            thList.add(th);
//
////            Thread th2 = this.createListOutReqTask(evseList.get(i).getFirst());
////            thList.add(th2);
//        }
//
//        Thread.sleep(3000);
//        for (int i = 0; i < thNum; i++) {
//            Thread th = thList.get(i);
//            th.join();
//        }
//        long endTime = System.currentTimeMillis();    // debug 性能问题
//        logger.error("total time = {}", ((double) (endTime - startTime)) / 1000);
//    }
//
//
//    private Thread createOrderTask(String evseId) {
//        var th = new Thread(new Runnable() {
//            @Override
//            public void run() {
//                long startTime = System.currentTimeMillis();    // debug 性能问题
//                for (int i = 0; i < 1000; i++) {
//                    createOrderA(evseId);
//
//                }
//                long endTime = System.currentTimeMillis();    // debug 性能问题
//                logger.error("total time = {}", ((double) (endTime - startTime)) / 1000);
//            }
//        });
//        th.start();
//        return th;
//    }
//
//    private void createOrderA(String evseId) {
//        long startTime2 = System.currentTimeMillis();    // debug 性能问题
//        var orderNo = UUID.randomUUID().toString().replace("-", "");
//        var orderRequest = new CreateOrderRequest() {{
//            setOrderNo(orderNo);
//            setEvseId(evseId);
////            setCarNo("11");
//        }};
//        PlugVo plugCache = new PlugVo();
//        plugCache.setEvseNo(orderRequest.getEvseId()).setIdx(orderRequest.getPlugId());
//        this.orderBizService.createOrder(orderRequest);
//        long time1 = System.currentTimeMillis(); // debug 性能问题
//        if (time1 - startTime2 > 50L) {
//            logger.error("xxxxxxxxxxxxxxxxxxxxxxxxx time = {}", time1 - startTime2);
//        }
//    }
//
//
//}