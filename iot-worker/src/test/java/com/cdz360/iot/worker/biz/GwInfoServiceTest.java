package com.cdz360.iot.worker.biz;

import com.cdz360.iot.ds.GwInfoService;
import com.cdz360.iot.model.site.dto.GwInfoDto;
import com.cdz360.iot.worker.IotWorkerTestBase;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class GwInfoServiceTest extends IotWorkerTestBase {
    private final Logger logger = LoggerFactory.getLogger(GwInfoServiceTest.class);

    @Autowired
    private GwInfoService gwInfoService;

    @Test
    public void doRegisterTest() {
        List<GwInfoDto> list = gwInfoService.getByMac("94-C6-91-29-5F-A3", true);

    }
}