<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.iot.ds.rw.mapper.SequenceRwMapper">

    <!-- TDengine不支持nextval函数，使用表模拟序列 -->
    <select id="getNextVal" resultType="java.lang.String">
        SELECT CONCAT(#{name}, LPAD(CAST(next_val AS CHAR), 6, '0')) as seq_value
        FROM t_sequence
        WHERE seq_name = #{name}
    </select>

    <!-- 更新序列值 -->
    <update id="updateNextVal">
        UPDATE t_sequence
        SET next_val = next_val + 1,
            update_time = NOW()
        WHERE seq_name = #{name}
    </update>

    <!-- 初始化序列 -->
    <insert id="initSequence">
        INSERT INTO t_sequence (seq_name, next_val, create_time, update_time)
        VALUES (#{name}, 1, NOW(), NOW())
        ON DUPLICATE KEY UPDATE seq_name = seq_name
    </insert>

</mapper>
