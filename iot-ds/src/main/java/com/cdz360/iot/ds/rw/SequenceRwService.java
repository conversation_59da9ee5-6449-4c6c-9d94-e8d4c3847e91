package com.cdz360.iot.ds.rw;

import com.cdz360.iot.ds.rw.mapper.SequenceRwMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.math.BigInteger;
import java.util.concurrent.atomic.AtomicLong;

@Service
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_SINGLETON)
public class SequenceRwService {

    private final String SRV_IDX = "SRV_IDX";   // 微服务ID序号

    private final String GWNO_IDX = "GWNO_IDX";   // 网关编号序号

    private final String CFG_VER_IDX = "CFG_VER_IDX";   // 桩配置版本序号

    @Autowired
    private SequenceRwMapper sequenceRwMapper;

    @Value("${env:}")
    private String env;

    private AtomicLong seq = new AtomicLong(1L);

    private String srvIdx = "";


    @PostConstruct
    public void postConstruct() {
        if("dev".equalsIgnoreCase(env)) {   // 单元测试不走这个逻辑
            return;
        }
        // 初始化序列
        initSequenceIfNotExists(SRV_IDX);
        initSequenceIfNotExists(GWNO_IDX);
        initSequenceIfNotExists(CFG_VER_IDX);

        this.srvIdx = this.getSrvIdx().substring(6).replaceFirst("0*", "");
    }

    public String getNextOutRequestSeq() {
        long timestamp = System.currentTimeMillis();
        StringBuilder buf = new StringBuilder();
        BigInteger v;
        synchronized (seq) {
            buf.append(timestamp).append(seq.getAndAdd(1L) % 999);
            v = new BigInteger(buf.toString());
        }
        return this.srvIdx + v.toString(16).toUpperCase();
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getSrvIdx() {
        return this.sequenceRwMapper.getNextVal(SRV_IDX);
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextGwno() {
        return this.sequenceRwMapper.getNextVal(GWNO_IDX);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNextCfgVer() {
        return this.sequenceRwMapper.getNextVal(CFG_VER_IDX);
    }
}
