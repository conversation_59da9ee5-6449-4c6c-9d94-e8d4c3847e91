package com.cdz360.iot.device.monitor.utils;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.iot.model.base.BaseRpcResponse;

public class IotResponseValidator {
    /**
     * 如果返回消息的 status != 0 则抛出异常
     *
     * @param res
     */
    public static void check(BaseRpcResponse res) {
        if (res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
            return;
        } else if (res.getStatus() == DcConstants.KEY_RES_CODE_TOKEN_ERROR) {
            throw new DcTokenException(res.getStatus(), res.getError());
        } else if (res.getStatus() == DcConstants.KEY_RES_CODE_SERVICE_ERROR) {
            throw new DcServiceException(res.getStatus(), res.getError());
        } else if (res.getStatus() == DcConstants.KEY_RES_CODE_ARGUMENT_ERROR) {
            throw new DcArgumentException(res.getStatus(), res.getError());
        } else if (res.getStatus() == DcConstants.KEY_RES_CODE_UNKNOWN_ERROR) {
            throw new DcServerException(res.getStatus(), res.getError());
        } else {
            throw new DcServerException(res.getStatus(), res.getError());
        }
    }
}
