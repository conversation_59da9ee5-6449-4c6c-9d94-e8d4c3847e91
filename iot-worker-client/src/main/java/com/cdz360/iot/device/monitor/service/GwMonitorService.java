package com.cdz360.iot.device.monitor.service;

import com.cdz360.iot.device.monitor.feign.GwMonitorClient;
import com.cdz360.iot.device.monitor.utils.IotResponseValidator;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.ListRpcResponse;
import com.cdz360.iot.model.site.param.GwParam;
import com.cdz360.iot.model.site.param.ListGwLogParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GwMonitorService {
    @Autowired
    private GwMonitorClient gwMonitorClient;

    @Deprecated
    public List<String> listGwno(ListGwLogParam param) {
        ListRpcResponse<String> res = gwMonitorClient.listGwno(param);
        IotResponseValidator.check(res);
        return res.getData();
    }

    /**
     * 通知更新网关状态
     * @param param
     */
    public void notifi(GwParam param) {
        BaseRpcResponse res = gwMonitorClient.notifi(param);
        IotResponseValidator.check(res);
        return;
    }
}
