package com.cdz360.iot.device.monitor.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.iot.device.monitor.cfg.WorkerUrl;
import com.cdz360.iot.model.base.BaseRpcResponse;
import com.cdz360.iot.model.base.ListRpcResponse;
import com.cdz360.iot.model.site.param.GwParam;
import com.cdz360.iot.model.site.param.ListGwLogParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = DcConstants.KEY_FEIGN_IOT_WORKER)
public interface GwMonitorClient {
    /**
     * 获取临时日志表中的场站列表
     *
     * @return
     */
    @PostMapping(value = WorkerUrl.URL_MONITOR_LIST_GWNO)
    ListRpcResponse<String> listGwno(@RequestBody ListGwLogParam param);

    /**
     * 通知更新网关状态
     *
     * @return
     */
    @PostMapping(value = WorkerUrl.URL_MONITOR_NOTIFY)
    BaseRpcResponse notifi(@RequestBody GwParam param);

    @PostMapping(value = WorkerUrl.URL_MONITOR_CHECK_SITE_CTRL_CFG_TIMEOUT)
    BaseResponse siteCtrlCfgTimeout(@RequestParam(value = "bufferTime", required = false) Integer bufferTime);
}