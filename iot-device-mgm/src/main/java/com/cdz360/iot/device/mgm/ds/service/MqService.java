package com.cdz360.iot.device.mgm.ds.service;

import com.cdz360.data.sync.service.DcEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname MqService
 * @Description
 * @Date 1/2/2020 10:10 AM
 * @Created by Rafael
 */

@Slf4j
@Service("deviceMgmMqService")
public class MqService {


    @Autowired
    private DcEventPublisher dcEventPublisher;

    public void publishMessage(String gwno, String msg) {
        this.dcEventPublisher.publishIotGwCmd(gwno, msg);
    }
}